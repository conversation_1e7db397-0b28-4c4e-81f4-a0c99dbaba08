// QRSAMS - QR Code Student Attendance Management System
// Comprehensive database schema for Philippine Grade 7-12 education system
// Optimized for DepEd compliance and local deployment with SQLite

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// ============================================================================
// CORE MODELS
// ============================================================================

// Student Model - Core student information with DepEd compliance
model Student {
  id          String     @id @default(cuid())
  studentId   String     @unique // DepEd format (e.g., 123456789012)
  firstName   String
  lastName    String
  middleName  String?
  email       String?
  dateOfBirth DateTime?
  gender      Gender?
  gradeLevel  GradeLevel
  section     String?
  course      String // Track/Strand (STEM, HUMSS, ABM, etc.)
  year        String // School year enrolled

  // Status and enrollment
  status         StudentStatus @default(ACTIVE)
  enrollmentDate DateTime      @default(now())

  // Guardian information
  guardianName         String
  guardianPhone        String
  guardianEmail        String?
  guardianRelationship GuardianRelationship

  // Emergency contact
  emergencyContactName         String
  emergencyContactPhone        String
  emergencyContactRelationship String?

  // Address (Tanauan, Leyte focus)
  address      String
  barangay     String?
  municipality String  @default("Tanauan")
  province     String  @default("Leyte")
  zipCode      String?

  // Media and QR
  photoUrl   String?
  qrCodeData String? // Generated QR code data

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  attendanceRecords  Attendance[]
  attendancePatterns AttendancePattern[]
  riskAssessments    StudentRiskAssessment[]
  smsLogs            SMSLog[]

  // Indexes for performance
  @@index([gradeLevel, section])
  @@index([status])
  @@index([guardianPhone])
  @@index([municipality, province])
  @@map("students")
}

// Teacher Model - Faculty and staff information
model Teacher {
  id          String  @id @default(cuid())
  employeeId  String  @unique
  firstName   String
  lastName    String
  middleName  String?
  email       String? @unique
  phoneNumber String?

  // Professional information
  role        TeacherRole @default(TEACHER)
  subjects    String? // JSON array of subject codes
  gradeLevels String? // JSON array of grade levels they teach
  sections    String? // JSON array of sections they handle

  // Employment details
  status   EmployeeStatus @default(ACTIVE)
  hireDate DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subjectsTeaching  Subject[]
  attendanceScanned Attendance[] @relation("ScannedByTeacher")

  // Indexes for performance
  @@index([role])
  @@index([status])
  @@map("teachers")
}

// Subject Model - Academic subjects and schedules
model Subject {
  id          String     @id @default(cuid())
  subjectCode String     @unique
  subjectName String
  gradeLevel  GradeLevel
  credits     Int        @default(1)
  room        String?

  // Schedule information (JSON format for flexibility)
  schedule String? // JSON: [{"day": "Monday", "startTime": "08:00", "endTime": "09:00"}]

  // Teacher assignment
  teacherId String?
  teacher   Teacher? @relation(fields: [teacherId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  attendanceRecords Attendance[]

  // Indexes for performance
  @@index([gradeLevel])
  @@index([teacherId])
  @@map("subjects")
}

// Attendance Model - Core attendance tracking
model Attendance {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Date and time information
  date    DateTime // Date of attendance (YYYY-MM-DD)
  timeIn  DateTime?
  timeOut DateTime?

  // Attendance details
  attendanceType AttendanceType   @default(GATE)
  subjectId      String?
  subject        Subject?         @relation(fields: [subjectId], references: [id])
  status         AttendanceStatus @default(PRESENT)

  // Tracking information
  scannedBy        String? // Teacher ID who scanned
  scannedByTeacher Teacher? @relation("ScannedByTeacher", fields: [scannedBy], references: [id])
  notes            String?
  isManualEntry    Boolean  @default(false)

  // Location and device info
  location String? // Gate, Classroom, etc.
  deviceId String? // Scanner device identifier

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@unique([studentId, date, attendanceType, subjectId])
  @@index([date])
  @@index([studentId, date])
  @@index([status])
  @@index([attendanceType])
  @@map("attendance")
}

// ============================================================================
// ANALYTICS AND AI MODELS
// ============================================================================

// AttendancePattern Model - AI analytics for attendance patterns
model AttendancePattern {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Pattern metrics
  weeklyRate          Float // Weekly attendance rate (0-1)
  monthlyRate         Float // Monthly attendance rate (0-1)
  consecutiveAbsences Int     @default(0)
  latePattern         String? // JSON: frequency of late arrivals by day/time

  // Risk assessment
  riskScore Float     @default(0) // 0-100 risk score
  riskLevel RiskLevel @default(LOW)

  // AI predictions and insights
  predictions String? // JSON: AI predictions for future attendance
  insights    String? // JSON: AI-generated insights

  // Analysis metadata
  lastAnalyzed DateTime @default(now())
  dataPoints   Int      @default(0) // Number of attendance records analyzed

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([studentId])
  @@map("attendance_patterns")
}

// StudentRiskAssessment Model - Comprehensive risk evaluation
model StudentRiskAssessment {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Risk metrics
  riskScore          Float // 0-100 overall risk score
  riskLevel          RiskLevel
  dropoutProbability Float // 0-1 probability of dropout

  // Risk factors (JSON format for flexibility)
  riskFactors   String? // JSON array of risk factors
  interventions String? // JSON array of recommended interventions

  // Parent engagement metrics
  parentEngagementScore Float     @default(50) // 0-100 engagement score
  lastParentContact     DateTime?
  parentResponseRate    Float     @default(0) // 0-1 response rate to communications

  // Assessment metadata
  assessmentDate DateTime @default(now())
  nextReviewDate DateTime
  assessedBy     String? // Teacher/Admin who conducted assessment

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("student_risk_assessments")
}

// SMSLog Model - SMS communication tracking
model SMSLog {
  id             String @id @default(cuid())
  recipientPhone String
  message        String

  // SMS status tracking
  status       SMSStatus @default(PENDING)
  sentAt       DateTime?
  deliveredAt  DateTime?
  errorMessage String?

  // Related information
  relatedStudentId String?
  relatedStudent   Student?    @relation(fields: [relatedStudentId], references: [id])
  messageType      MessageType @default(GENERAL)

  // Metadata
  provider  String? // SMS provider used
  messageId String? // Provider's message ID
  cost      Float? // Cost in PHP

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([status])
  @@index([relatedStudentId])
  @@index([messageType])
  @@index([sentAt])
  @@map("sms_logs")
}

// ============================================================================
// ADDITIONAL ANALYTICS MODELS
// ============================================================================

// SystemSettings Model - Application configuration
model SystemSettings {
  id          String  @id @default(cuid())
  key         String  @unique
  value       String
  description String?
  category    String  @default("general")

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}

// AuditLog Model - System activity tracking
model AuditLog {
  id         String  @id @default(cuid())
  userId     String? // Teacher/Admin who performed action
  action     String // Action performed
  entityType String // Student, Teacher, Attendance, etc.
  entityId   String? // ID of affected entity

  // Change details
  oldValues String? // JSON of old values
  newValues String? // JSON of new values
  ipAddress String?
  userAgent String?

  // Timestamps
  createdAt DateTime @default(now())

  // Indexes for performance
  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([createdAt])
  @@map("audit_logs")
}

// NotificationQueue Model - Queued notifications
model NotificationQueue {
  id            String @id @default(cuid())
  recipientId   String // Student ID or Teacher ID
  recipientType String // "student" or "teacher"

  // Notification details
  type     MessageType
  title    String
  message  String
  priority NotificationPriority @default(NORMAL)

  // Delivery channels
  channels String // JSON array: ["sms", "email", "push"]

  // Status tracking
  status        NotificationStatus @default(PENDING)
  scheduledFor  DateTime?
  sentAt        DateTime?
  deliveredAt   DateTime?
  failureReason String?
  retryCount    Int                @default(0)
  maxRetries    Int                @default(3)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([status])
  @@index([scheduledFor])
  @@index([recipientId, recipientType])
  @@map("notification_queue")
}

// ============================================================================
// ENUMS
// ============================================================================

// Student-related enums
enum Gender {
  MALE
  FEMALE
}

enum GradeLevel {
  GRADE_7
  GRADE_8
  GRADE_9
  GRADE_10
  GRADE_11
  GRADE_12
}

enum StudentStatus {
  ACTIVE
  INACTIVE
  TRANSFERRED
  GRADUATED
  DROPPED
}

enum GuardianRelationship {
  FATHER
  MOTHER
  GUARDIAN
  GRANDPARENT
  SIBLING
  AUNT
  UNCLE
  OTHER
}

// Teacher-related enums
enum TeacherRole {
  TEACHER
  DEPARTMENT_HEAD
  GUIDANCE_COUNSELOR
  ADMIN
  PRINCIPAL
  VICE_PRINCIPAL
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE
  RESIGNED
  TERMINATED
  RETIRED
}

// Attendance-related enums
enum AttendanceType {
  GATE // School gate entry/exit
  SUBJECT // Subject-specific attendance
  EVENT // Special events
  ASSEMBLY // School assemblies
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
  HALF_DAY
}

// Analytics and risk-related enums
enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// SMS-related enums
enum SMSStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  EXPIRED
}

enum MessageType {
  ATTENDANCE_ALERT // Absence notifications
  LATE_ARRIVAL // Late arrival notifications
  RISK_WARNING // Risk assessment alerts
  GENERAL // General announcements
  EMERGENCY // Emergency notifications
  PARENT_MEETING // Meeting invitations
  ACADEMIC_UPDATE // Academic performance updates
}

// Notification-related enums
enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationStatus {
  PENDING
  SCHEDULED
  SENT
  DELIVERED
  FAILED
  CANCELLED
}
