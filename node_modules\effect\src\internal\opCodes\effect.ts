/** @internal */
export type OP_ASYNC = typeof OP_ASYNC

/** @internal */
export const OP_ASYNC = "Async" as const

/** @internal */
export type OP_COMMIT = typeof OP_COMMIT

/** @internal */
export const OP_COMMIT = "Commit" as const

/** @internal */
export type OP_FAILURE = typeof OP_FAILURE

/** @internal */
export const OP_FAILURE = "Failure" as const

/** @internal */
export type OP_ON_FAILURE = typeof OP_ON_FAILURE

/** @internal */
export const OP_ON_FAILURE = "OnFailure" as const

/** @internal */
export type OP_ON_SUCCESS = typeof OP_ON_SUCCESS

/** @internal */
export const OP_ON_SUCCESS = "OnSuccess" as const

/** @internal */
export type OP_ON_SUCCESS_AND_FAILURE = typeof OP_ON_SUCCESS_AND_FAILURE

/** @internal */
export const OP_ON_SUCCESS_AND_FAILURE = "OnSuccessAndFailure" as const

/** @internal */
export type OP_SUCCESS = typeof OP_SUCCESS

/** @internal */
export const OP_SUCCESS = "Success" as const

/** @internal */
export type OP_SYNC = typeof OP_SYNC

/** @internal */
export const OP_SYNC = "Sync" as const

/** @internal */
export const OP_TAG = "Tag" as const

/** @internal */
export type OP_TAG = typeof OP_TAG

/** @internal */
export type OP_UPDATE_RUNTIME_FLAGS = typeof OP_UPDATE_RUNTIME_FLAGS

/** @internal */
export const OP_UPDATE_RUNTIME_FLAGS = "UpdateRuntimeFlags" as const

/** @internal */
export type OP_WHILE = typeof OP_WHILE

/** @internal */
export const OP_WHILE = "While" as const

/** @internal */
export type OP_ITERATOR = typeof OP_ITERATOR

/** @internal */
export const OP_ITERATOR = "Iterator" as const

/** @internal */
export type OP_WITH_RUNTIME = typeof OP_WITH_RUNTIME

/** @internal */
export const OP_WITH_RUNTIME = "WithRuntime" as const

/** @internal */
export type OP_YIELD = typeof OP_YIELD

/** @internal */
export const OP_YIELD = "Yield" as const

/** @internal */
export type OP_REVERT_FLAGS = typeof OP_REVERT_FLAGS

/** @internal */
export const OP_REVERT_FLAGS = "RevertFlags" as const
