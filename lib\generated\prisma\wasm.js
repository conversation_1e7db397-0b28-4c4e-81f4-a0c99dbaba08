
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.StudentScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  firstName: 'firstName',
  lastName: 'lastName',
  middleName: 'middleName',
  email: 'email',
  dateOfBirth: 'dateOfBirth',
  gender: 'gender',
  gradeLevel: 'gradeLevel',
  section: 'section',
  course: 'course',
  year: 'year',
  status: 'status',
  enrollmentDate: 'enrollmentDate',
  guardianName: 'guardianName',
  guardianPhone: 'guardianPhone',
  guardianEmail: 'guardianEmail',
  guardianRelationship: 'guardianRelationship',
  emergencyContactName: 'emergencyContactName',
  emergencyContactPhone: 'emergencyContactPhone',
  emergencyContactRelationship: 'emergencyContactRelationship',
  address: 'address',
  barangay: 'barangay',
  municipality: 'municipality',
  province: 'province',
  zipCode: 'zipCode',
  photoUrl: 'photoUrl',
  qrCodeData: 'qrCodeData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TeacherScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  firstName: 'firstName',
  lastName: 'lastName',
  middleName: 'middleName',
  email: 'email',
  phoneNumber: 'phoneNumber',
  role: 'role',
  subjects: 'subjects',
  gradeLevels: 'gradeLevels',
  sections: 'sections',
  status: 'status',
  hireDate: 'hireDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubjectScalarFieldEnum = {
  id: 'id',
  subjectCode: 'subjectCode',
  subjectName: 'subjectName',
  gradeLevel: 'gradeLevel',
  credits: 'credits',
  room: 'room',
  schedule: 'schedule',
  teacherId: 'teacherId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AttendanceScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  date: 'date',
  timeIn: 'timeIn',
  timeOut: 'timeOut',
  attendanceType: 'attendanceType',
  subjectId: 'subjectId',
  status: 'status',
  scannedBy: 'scannedBy',
  notes: 'notes',
  isManualEntry: 'isManualEntry',
  location: 'location',
  deviceId: 'deviceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AttendancePatternScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  weeklyRate: 'weeklyRate',
  monthlyRate: 'monthlyRate',
  consecutiveAbsences: 'consecutiveAbsences',
  latePattern: 'latePattern',
  riskScore: 'riskScore',
  riskLevel: 'riskLevel',
  predictions: 'predictions',
  insights: 'insights',
  lastAnalyzed: 'lastAnalyzed',
  dataPoints: 'dataPoints',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentRiskAssessmentScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  riskScore: 'riskScore',
  riskLevel: 'riskLevel',
  dropoutProbability: 'dropoutProbability',
  riskFactors: 'riskFactors',
  interventions: 'interventions',
  parentEngagementScore: 'parentEngagementScore',
  lastParentContact: 'lastParentContact',
  parentResponseRate: 'parentResponseRate',
  assessmentDate: 'assessmentDate',
  nextReviewDate: 'nextReviewDate',
  assessedBy: 'assessedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SMSLogScalarFieldEnum = {
  id: 'id',
  recipientPhone: 'recipientPhone',
  message: 'message',
  status: 'status',
  sentAt: 'sentAt',
  deliveredAt: 'deliveredAt',
  errorMessage: 'errorMessage',
  relatedStudentId: 'relatedStudentId',
  messageType: 'messageType',
  provider: 'provider',
  messageId: 'messageId',
  cost: 'cost',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemSettingsScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  category: 'category',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationQueueScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  recipientType: 'recipientType',
  type: 'type',
  title: 'title',
  message: 'message',
  priority: 'priority',
  channels: 'channels',
  status: 'status',
  scheduledFor: 'scheduledFor',
  sentAt: 'sentAt',
  deliveredAt: 'deliveredAt',
  failureReason: 'failureReason',
  retryCount: 'retryCount',
  maxRetries: 'maxRetries',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.Gender = exports.$Enums.Gender = {
  MALE: 'MALE',
  FEMALE: 'FEMALE'
};

exports.GradeLevel = exports.$Enums.GradeLevel = {
  GRADE_7: 'GRADE_7',
  GRADE_8: 'GRADE_8',
  GRADE_9: 'GRADE_9',
  GRADE_10: 'GRADE_10',
  GRADE_11: 'GRADE_11',
  GRADE_12: 'GRADE_12'
};

exports.StudentStatus = exports.$Enums.StudentStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  TRANSFERRED: 'TRANSFERRED',
  GRADUATED: 'GRADUATED',
  DROPPED: 'DROPPED'
};

exports.GuardianRelationship = exports.$Enums.GuardianRelationship = {
  FATHER: 'FATHER',
  MOTHER: 'MOTHER',
  GUARDIAN: 'GUARDIAN',
  GRANDPARENT: 'GRANDPARENT',
  SIBLING: 'SIBLING',
  AUNT: 'AUNT',
  UNCLE: 'UNCLE',
  OTHER: 'OTHER'
};

exports.TeacherRole = exports.$Enums.TeacherRole = {
  TEACHER: 'TEACHER',
  DEPARTMENT_HEAD: 'DEPARTMENT_HEAD',
  GUIDANCE_COUNSELOR: 'GUIDANCE_COUNSELOR',
  ADMIN: 'ADMIN',
  PRINCIPAL: 'PRINCIPAL',
  VICE_PRINCIPAL: 'VICE_PRINCIPAL'
};

exports.EmployeeStatus = exports.$Enums.EmployeeStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  RESIGNED: 'RESIGNED',
  TERMINATED: 'TERMINATED',
  RETIRED: 'RETIRED'
};

exports.AttendanceType = exports.$Enums.AttendanceType = {
  GATE: 'GATE',
  SUBJECT: 'SUBJECT',
  EVENT: 'EVENT',
  ASSEMBLY: 'ASSEMBLY'
};

exports.AttendanceStatus = exports.$Enums.AttendanceStatus = {
  PRESENT: 'PRESENT',
  ABSENT: 'ABSENT',
  LATE: 'LATE',
  EXCUSED: 'EXCUSED',
  HALF_DAY: 'HALF_DAY'
};

exports.RiskLevel = exports.$Enums.RiskLevel = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.SMSStatus = exports.$Enums.SMSStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  DELIVERED: 'DELIVERED',
  FAILED: 'FAILED',
  EXPIRED: 'EXPIRED'
};

exports.MessageType = exports.$Enums.MessageType = {
  ATTENDANCE_ALERT: 'ATTENDANCE_ALERT',
  LATE_ARRIVAL: 'LATE_ARRIVAL',
  RISK_WARNING: 'RISK_WARNING',
  GENERAL: 'GENERAL',
  EMERGENCY: 'EMERGENCY',
  PARENT_MEETING: 'PARENT_MEETING',
  ACADEMIC_UPDATE: 'ACADEMIC_UPDATE'
};

exports.NotificationPriority = exports.$Enums.NotificationPriority = {
  LOW: 'LOW',
  NORMAL: 'NORMAL',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.NotificationStatus = exports.$Enums.NotificationStatus = {
  PENDING: 'PENDING',
  SCHEDULED: 'SCHEDULED',
  SENT: 'SENT',
  DELIVERED: 'DELIVERED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  Student: 'Student',
  Teacher: 'Teacher',
  Subject: 'Subject',
  Attendance: 'Attendance',
  AttendancePattern: 'AttendancePattern',
  StudentRiskAssessment: 'StudentRiskAssessment',
  SMSLog: 'SMSLog',
  SystemSettings: 'SystemSettings',
  AuditLog: 'AuditLog',
  NotificationQueue: 'NotificationQueue'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
